const { By, until } = require('selenium-webdriver');

async function waitForPageLoad(driver, timeout = 15000) {
  try {
    await driver.executeScript('return document.readyState').then(state => {
      if (state !== 'complete') {
        return driver.wait(() => driver.executeScript('return document.readyState === "complete"'), timeout);
      }
    });
  } catch (err) {
    console.error('Error waiting for page load:', err);
    console.error('Page source:', await driver.getPageSource());
    throw err;
  }
}

module.exports = { waitForPageLoad };