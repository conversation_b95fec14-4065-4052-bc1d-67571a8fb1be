<nz-sider>
  <div class="form-menu ml-10">
    <div class="form-synapse mt-20 mb-20">
      <h3>Synapse</h3>
      <ul class="item-menu" nz-menu nzMode="inline">
        <li nz-menu-item [nzSelected]="isActive('/protected')" routerLink="/protected">
          <nz-icon nzType="fund-view" nzTheme="outline" />
          <span>Overview</span>
        </li>
        <li nz-menu-item [nzSelected]="isActive('/all-users')" routerLink="/all-users">
          <nz-icon nzType="user" nzTheme="outline" />
          <span>All Users</span>
        </li>
        <li id="select-organizations" nz-menu-item [nzSelected]="isActive('/organizations')"
          routerLink="/organizations">
          <nz-icon nzType="team" nzTheme="outline" />
          <span>All Organizations</span>
        </li>
        <li class="submenu-item" nz-submenu [(nzOpen)]="openMap['sub1']" (nzOpenChange)="openHandler('sub1')"
          nzTitle="Admin" nzIcon="setting">
          <ul>
            <li nz-menu-item [nzSelected]="isActive('/permissions')" routerLink="/permissions">
              Permissions
            </li>
            <li nz-menu-item [nzSelected]="isActive('/users-admin')" routerLink="/users-admin">
              Users
            </li>
          </ul>
        </li>
      </ul>
    </div>
    <!-- <div class="form-organizations mb-20">
      <h3>Organizations Foo</h3>
      <ul class="item-menu" nz-menu nzMode="inline">
        <li nz-menu-item [nzSelected]="isActive('/organizations')" routerLink="/organizations">
          <nz-icon nzType="fund-view" nzTheme="outline" />
          <span>Overview</span>
        </li>
        <li nz-menu-item [nzSelected]="isActive('/software-gateway')" routerLink="/software-gateway">
          <nz-icon nzType="cluster" nzTheme="outline" />
          <span>Gateways</span>
        </li>
        <li nz-menu-item [nzSelected]="isActive('/Devices')" routerLink="/Devices">
          <nz-icon nzType="desktop" nzTheme="outline" />
          <span>Devices</span>
        </li>
        <li class="submenu-item" nz-submenu [(nzOpen)]="openMap['sub2']" (nzOpenChange)="openHandler('sub2')"
          nzTitle="Admin" nzIcon="setting">
          <ul>
            <li nz-menu-item [nzSelected]="isActive('/DeviceGroups')" routerLink="/DeviceGroups">
              Device Groups
            </li>
            <li nz-menu-item [nzSelected]="isActive('/Locations')" routerLink="/Locations">
              Locations
            </li>
          </ul>
        </li>
        <li nz-menu-item [nzSelected]="isActive('/Notifications')" routerLink="/Notifications">
          <nz-icon nzType="notification" nzTheme="outline" />
          <span>My Notifications</span>
        </li>
      </ul>
    </div> -->
    <!-- <div class="form-account mb-20">
      <h3>My Account</h3>
      <ul class="item-menu" nz-menu nzMode="inline">
        <li nz-menu-item [nzSelected]="isActive('/Info')" routerLink="/Info">
          <nz-icon nzType="info-circle" nzTheme="outline" />
          <span>Info</span>
        </li>
        <li nz-menu-item [nzSelected]="isActive('/Authentication')" routerLink="/Authentication">
          <nz-icon nzType="safety-certificate" nzTheme="outline" />
          <span>Authentication Methods</span>
        </li>
        <li nz-menu-item [nzSelected]="isActive('/Invitations')" routerLink="/Invitations">
          <nz-icon nzType="send" nzTheme="outline" />
          <span>Invitations Pending</span>
        </li>
        <li nz-menu-item [nzSelected]="isActive('/Preferences')" routerLink="/Preferences">
          <nz-icon nzType="block" nzTheme="outline" />
          <span>Preferences</span>
        </li>
      </ul>
    </div> -->
  </div>
</nz-sider>