import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { RolesPermissionsService } from '../../../core/services/roles_permissions.service';

@Component({
  selector: 'app-permissions',
  standalone: false,
  templateUrl: './permissions.component.html',
  styleUrls: ['./permissions.component.css']
})
export class PermissionsComponent implements OnChanges {
  @Input() dataPermissions: any[] = [];
  @Input() orgId: any[] = [];
  @Input() dataRole: any[] = [];
  sortedPermissions: any[] = [];
  isTableLoading = false;

  constructor(
    private rolesPermissionsService: RolesPermissionsService,
  ) { }
  ngOnChanges(changes: SimpleChanges): void {
    this.isTableLoading = true;
    if (changes['dataPermissions'] && this.dataPermissions) {
      this.sortedPermissions = [...this.dataPermissions].sort((a, b) => a.weight - b.weight);
      this.isTableLoading = false;
    }
  }

  getPermissionNames(roles: any[]): string[] {
    const permissionsMap = new Map<string, number>();
    roles.forEach(role => {
      role.permissions.forEach((perm: any) => {
        permissionsMap.set(perm.name, perm.weight);
      });
    });
    return Array.from(permissionsMap.keys()).sort((a, b) => {
      const weightA = permissionsMap.get(a) || 0;
      const weightB = permissionsMap.get(b) || 0;
      return weightA - weightB;
    });
  }
  getLabelFromValue(name: string): string | undefined {
    const role = this.dataRole.find(option => option.name === name);
    return role ? role.id : undefined;
  }

  onPermissionChange(permission: any, role: any, permissionGroup: any): void {
    this.isTableLoading = true;
    if (permission.inherited) {
      permission.inherited = false;
    }
    const getIdRole = this.getLabelFromValue(role.name)
    const formPayload = {
      value: permission.value,
    }
    this.rolesPermissionsService.updatePermissions(this.orgId, permission.machine_name, getIdRole, formPayload).subscribe((res) => {
      this.isTableLoading = false;
    }, (error) => {
      console.error('Error fetching Gateway list:', error);
      this.isTableLoading = false;
    })
  }
}