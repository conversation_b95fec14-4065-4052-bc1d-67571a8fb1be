<div class="permissions-container">
  <div>
    <div class="title-permissions">
      <h1 id="title-roles-permissions">Roles and Permissions</h1>
    </div>
    <div class="form-breadcrumb">
      <nz-breadcrumb>
        <nz-breadcrumb-item>
          Synapse
        </nz-breadcrumb-item>
        <nz-breadcrumb-item>
          Admin
        </nz-breadcrumb-item>
        <nz-breadcrumb-item>
          Roles and Permissions
        </nz-breadcrumb-item>
      </nz-breadcrumb>
    </div>
    <div class="form-description-title">
      <span>This page allows you to manage the Roles and Permissions that are active for this Organization.</span>
    </div>
  </div>
  <div class="container-table">
    <div class="form-header">
      <div class="title">
        <h1>Roles</h1>
      </div>
      <div class="btn-add">
        <button id="btn-create-role-modal" nz-button nzType="primary" class="add-btn br-8 h-4" (click)="openAddPopup()">
          Create New Role
        </button>
      </div>
    </div>
    <div class="form-description-roles">
      <span>Giving a Role to a user is the same as giving them a set of permissions. When a role is granted to a user as
        part of their Organization membership, then they are granted the organization-level permissions (e.g., Manager).
        When a role is granted to a user as part of a group (e.g., a Device Group), then they are granted permissions
        only within the context of that group. For example, if a user receives the role of “Technician” as part of their
        organization membership, they will not also be a “Technician” within a Device Group unless they are also added
        to the device group as a “Technician”. This allows the greatest flexibility in access control with the
        simplicity of clear Roles and Permissions.</span>
    </div>
    <nz-table id="permissions-list" #basicTable nzBordered [nzData]="listOfData" nzShowSizeChanger nzShowPagination
      [nzLoading]="isTableLoading">
      <thead>
        <tr>
          <th nzWidth="30%">Name</th>
          <th nzWidth="30%">Description</th>
          <th nzWidth="30%">Template</th>
          <th nzWidth="5%">Action</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of basicTable.data; let i = index"
          [ngClass]="data.id === highlightedRowId ? 'highlight-row' : 'default-row'">
          <td>{{ data.name }}</td>
          <td>{{ data.description }}</td>
          <td>{{ getLabelFromValue(data.templateRoleIdentifier )}}</td>
          <td>
            <div class="btn-action">
              <button nz-button nzType="text" (click)="hanldeDelete(data)" nzDanger nz-tooltip nzTooltipTitle="Delete">
                <nz-icon nzType="delete" nzTheme="outline" />
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </nz-table>
    <app-add-edit-role [orgId]="orgIdRole" [listRolesTemp]="templateOptions" [isVisible]="isModalVisible"
      [isEditMode]="isEditMode" [isDetail]="isDetailOrg" (close)="closeModal()" (confirm)="handleModalSave($event)">
    </app-add-edit-role>
  </div>
  <app-permissions [dataRole]="listOfData" [orgId]="orgIdRole" [dataPermissions]="dataPermissions"></app-permissions>
</div>