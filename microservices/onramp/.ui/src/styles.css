/* You can add global styles to this file, and also import other style files */

html,
body {
  width: 100%;
  overflow: auto;
  height: 100%;
}

body {
  margin: 0;
  font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif;
}

.ant-table-thead tr th {
  font-weight: bold;
}

.custom-confirm-modal .ant-modal-confirm-content {
  background: #ff00001f;
  padding: 10px;
  border-radius: 8px;
  color: #7a1919cc;
  margin: 25px 0px;
  margin-left: unset !important;
}

.custom-login-modal-required .ant-modal-content,
.custom-login-modal-permissions .ant-modal-content {
  border-radius: 8px;
}

.custom-login-modal-required .ant-modal-content .ant-modal-confirm-btns button,
.custom-login-modal-permissions .ant-modal-content .ant-modal-confirm-btns button {
  width: 80px;
  height: 40px;
  border-radius: 8px;
}

nz-message-container .ant-message-notice {
  margin-top: 50px;
}

/* css spacing utilities */
.mb-8 {
  margin-bottom: 8px !important;
}

.mt-20 {
  margin-top: 20px !important;
}

.mb-20 {
  margin-bottom: 20px !important;
}

.mt-24 {
  margin-top: 24px !important;
}

.mb-24 {
  margin-bottom: 24px !important;
}

.mr-5 {
  margin-right: 5px !important;
}

.ml-10 {
  margin-left: 10px !important;
}

.p-0 {
  padding: 0 !important;
}

.br-8 {
  border-radius: 8px !important;
}

.br-12 {
  border-radius: 12px !important;
}

.h-4 {
  height: 40px !important;
}