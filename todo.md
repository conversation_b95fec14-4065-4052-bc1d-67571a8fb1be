# BigQuery Batcher Max Queue Size Implementation

## Overview

Implement configurable maximum queue size for BigQuery batcher to apply back-pressure on Pub/Sub when downstream writes stall, preventing out-of-memory crashes.

## Problem Statement

- Current `batcher.Add()` is fully non-blocking
- Messages are ACKed immediately after enqueueing
- When BigQuery is slow/unavailable, in-memory queues grow without bound
- Leads to out-of-memory crashes
- Pub/Sub flow control never activates because messages are ACKed before successful write

## Solution

Add configurable `MaxQueueDepth` parameter that blocks `batcher.Add()` when queue reaches capacity, causing Pub/Sub handlers to stop ACKing messages and activate flow control.

## Implementation Tasks

### Phase 1: Configuration and Structure Updates

#### 1.1 Extend QueueConfig Structure
- [x] **File**: `shared/bqbatch/schemas.go`
- [x] Add `MaxQueueDepth int` field to `QueueConfig` struct
- [x] Add documentation comment explaining the field purpose
- [x] Ensure backward compatibility (0 = unlimited)

#### 1.2 Add Environment Variable Support
- [x] **File**: `shared/bqbatch/bqbatch.go`
- [x] Add `BQBATCH_MAX_QUEUE_DEPTH` environment variable parsing in `init()` function
- [x] Add validation for non-negative values
- [x] Set global `MaxQueueDepth` variable

#### 1.3 Add Global Configuration Variable
- [x] **File**: `shared/bqbatch/bqbatch.go`
- [x] Add `MaxQueueDepth` to global variables section
- [x] Set default value to 0 (unlimited for backward compatibility)
- [x] Add documentation comment

### Phase 2: Queue Structure Updates

#### 2.1 Add Queue Depth Tracking Fields
- [x] **File**: `shared/bqbatch/bqbatch.go`
- [x] Add `currentDepth int32` (atomic counter) to `queue` struct
- [x] Add `maxDepth int` field to `queue` struct
- [x] Add `depthMu sync.Mutex` for depth operations
- [x] Add `depthCond *sync.Cond` for blocking/unblocking

#### 2.2 Update newQueue Function
- [x] **File**: `shared/bqbatch/bqbatch.go`
- [x] Modify `newQueue()` to initialize new depth tracking fields
- [x] Set `maxDepth` from `QueueConfig.MaxQueueDepth`
- [x] Initialize `depthCond` with `depthMu`
- [x] Ensure proper initialization order

### Phase 3: Implement Blocking Add Logic

#### 3.1 Modify batcher.Add Method
- [x] **File**: `shared/bqbatch/bqbatch.go`
- [x] Add depth checking before adding rows
- [x] Implement blocking logic using condition variables
- [x] Add atomic increment of `currentDepth`
- [x] Maintain existing row addition logic
- [x] Ensure proper error handling

#### 3.2 Add Shutdown Handling
- [x] **File**: `shared/bqbatch/bqbatch.go`
- [x] Add shutdown check in blocking loop
- [x] Return appropriate error when batcher is shutting down
- [x] Ensure blocked operations are woken up during shutdown

### Phase 4: Update Flush Logic

#### 4.1 Modify flushAsync Method
- [x] **File**: `shared/bqbatch/bqbatch.go`
- [x] Add depth decrement after copying rows
- [x] Signal blocked operations using `depthCond.Broadcast()`
- [x] Maintain existing async flush behavior
- [x] Ensure atomic operations for depth tracking

#### 4.2 Update flushSync Method
- [x] **File**: `shared/bqbatch/bqbatch.go`
- [x] Add depth decrement logic similar to flushAsync
- [x] Ensure proper synchronization
- [x] Maintain existing sync flush behavior

### Phase 5: Update Default Configuration

#### 5.1 Set Reasonable Defaults
- [x] **File**: `shared/bqbatch/bqbatch.go`
- [x] Update `newDefault()` function
- [x] Set `MaxQueueDepth: 5000` in default config (5x flush size)
- [x] Ensure backward compatibility for existing deployments

### Phase 6: Error Handling and Edge Cases

#### 6.1 Add New Error Types
- [x] **File**: `shared/bqbatch/bqbatch.go`
- [x] Add `ErrBatcherIsShutDown` error (already exists)
- [x] Add `ErrQueueDepthExceeded` error (if needed) - not needed as we use blocking
- [x] Ensure proper error documentation

#### 6.2 Update Shutdown Method
- [x] **File**: `shared/bqbatch/bqbatch.go`
- [x] Modify `Shutdown()` to wake up blocked operations
- [x] Call `depthCond.Broadcast()` for all queues
- [x] Ensure proper cleanup order

### Phase 7: Configuration Validation

#### 7.1 Add Validation Logic
- [x] **File**: `shared/bqbatch/bqbatch.go`
- [x] Add validation in `Register()` method
- [x] Check for negative `MaxQueueDepth` values
- [x] Add warning when `MaxQueueDepth < MaxSize`
- [x] Ensure validation doesn't break existing functionality

### Phase 8: Testing Implementation

#### 8.1 Unit Tests - Basic Functionality
- [x] **File**: `shared/bqbatch/bqbatch_test.go`
- [x] Test normal operation with unlimited depth
- [x] Test blocking behavior with limited depth
- [x] Test unblocking after flush
- [x] Test shutdown behavior

#### 8.2 Unit Tests - Edge Cases
- [x] **File**: `shared/bqbatch/bqbatch_test.go`
- [x] Test with `MaxQueueDepth = 0` (unlimited)
- [x] Test with `MaxQueueDepth = 1`
- [x] Test concurrent Add operations
- [x] Test rapid flush/unblock cycles

#### 8.3 Integration Tests
- [ ] **File**: `microservices/testing/integration/shared/bqbatch_backpressure_test.go`
- [ ] Test with real BigQuery under load
- [ ] Test back-pressure activation
- [ ] Test memory usage under stress
- [ ] Test recovery scenarios

### Phase 9: Metrics and Observability

#### 9.1 Add Queue Depth Metrics
- [ ] **File**: `shared/bqbatch/schemas.go`
- [ ] Add `QueueDepthStats` struct
- [ ] Include fields for current depth, max depth, blocked operations
- [ ] Add timestamp and table identification

#### 9.2 Track Blocking Events
- [ ] **File**: `shared/bqbatch/bqbatch.go`
- [ ] Add atomic counters for blocked operations
- [ ] Track total blocking time
- [ ] Calculate average block time
- [ ] Ensure thread-safe metrics collection

### Phase 10: Performance Optimizations

#### 10.1 Optimize Condition Variable Usage
- [ ] **File**: `shared/bqbatch/bqbatch.go`
- [ ] Ensure separate mutex for depth operations
- [ ] Minimize lock contention
- [ ] Optimize broadcast frequency

#### 10.2 Batch Depth Updates
- [ ] **File**: `shared/bqbatch/bqbatch.go`
- [ ] Decrement depth immediately after copying rows
- [ ] Avoid unnecessary atomic operations
- [ ] Optimize for high-throughput scenarios

### Phase 11: Documentation and Deployment

#### 11.1 Update Documentation
- [ ] **File**: `shared/bqbatch/README.md` (create if needed)
- [ ] Document new `MaxQueueDepth` parameter
- [ ] Explain back-pressure mechanism
- [ ] Provide configuration examples
- [ ] Include troubleshooting guide

#### 11.2 Environment Configuration
- [ ] **File**: Deployment configuration files
- [ ] Add `BQBATCH_MAX_QUEUE_DEPTH` environment variable
- [ ] Set conservative initial values (5000)
- [ ] Document tuning guidelines

### Phase 12: Monitoring and Alerting

#### 12.1 Add Monitoring Metrics
- [ ] **File**: Monitoring configuration
- [ ] Queue depth per table
- [ ] Blocked Add operations count
- [ ] Average block time
- [ ] Back-pressure activation frequency

#### 12.2 Configure Alerts
- [ ] **File**: Alerting configuration
- [ ] Alert when queue depth > 80% of limit
- [ ] Alert when Add operations blocked > 30 seconds
- [ ] Alert on memory usage spikes (should not occur with fix)

## Testing Scenarios

### Scenario 1: Normal Operation
- [ ] Queue depth stays below limit
- [ ] No blocking occurs
- [ ] Performance unchanged from current behavior

### Scenario 2: BigQuery Slowdown
- [ ] Queue depth reaches limit
- [ ] Add operations block appropriately
- [ ] Pub/Sub flow control activates
- [ ] Memory usage stabilizes

### Scenario 3: BigQuery Outage
- [ ] All queues reach capacity
- [ ] All Add operations block
- [ ] Pub/Sub stops delivering messages
- [ ] System remains stable

### Scenario 4: Recovery
- [ ] BigQuery comes back online
- [ ] Queues drain properly
- [ ] Blocked operations resume
- [ ] Normal operation resumes

## Risk Mitigation

### Backward Compatibility
- [ ] Default `MaxQueueDepth = 0` (unlimited)
- [ ] Existing deployments continue to work unchanged
- [ ] Gradual rollout capability

### Graceful Degradation
- [ ] System remains functional under back-pressure
- [ ] Proper error handling during shutdown
- [ ] No data loss during blocking

### Escape Hatch
- [ ] Environment variable to disable feature
- [ ] Ability to set unlimited depth
- [ ] Clear operational procedures

## Deployment Strategy

### Phase 1: Conservative Rollout
- [ ] Deploy with high limits (5000)
- [ ] Monitor memory usage and performance
- [ ] Verify no performance regression

### Phase 2: Gradual Tuning
- [ ] Reduce limits based on monitoring
- [ ] Test under various load conditions
- [ ] Optimize for specific use cases

### Phase 3: Production Validation
- [ ] Full production deployment
- [ ] Monitor all metrics
- [ ] Validate back-pressure effectiveness

## Success Criteria

- [ ] No out-of-memory crashes during BigQuery slowdowns
- [ ] Pub/Sub flow control activates when needed
- [ ] System remains stable under stress
- [ ] Performance impact is minimal during normal operation
- [ ] Backward compatibility maintained
- [ ] Comprehensive monitoring and alerting in place

## Notes

- All changes should maintain backward compatibility
- Focus on thread-safe implementation
- Ensure proper error handling and logging
- Test thoroughly under various load conditions
- Monitor performance impact during implementation 