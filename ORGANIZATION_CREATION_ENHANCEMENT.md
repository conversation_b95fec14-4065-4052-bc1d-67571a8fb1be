# Organization Creation Enhancement

## Overview

This document outlines the implementation of enhanced organization creation functionality that automatically creates CustomRoles and DeviceGroups when a new Organization is created, and enforces role immutability for critical system roles.

## Requirements Implemented

### 1. Enforce Role Immutability

**Database Changes:**
- Added `IsDeletable BOOLEAN NOT NULL DEFAULT TRUE` to the `{{TemplateRole}}` table
- Added `IsDeletable BOOLEAN NOT NULL DEFAULT TRUE` to the `{{CustomRole}}` table
- Created migration file `add_isdeletable_columns.sql` to update existing data

**Seed Data Updates:**
- Set `IsDeletable = FALSE` for admin and anonymous roles:
  - `mun_admin` (Municipality Admin)
  - `mun_anonymous` (Municipality Anonymous)
  - `syn_admin` (Synapse Admin)
  - `syn_anonymous` (Synapse Anonymous)
- Set `IsDeletable = TRUE` for all other roles (manager, technician, onboarder)

**Application Logic:**
- Updated `deleteRole` function to check `IsDeletable` before allowing deletion
- Added `ErrRoleNotDeletable` error for non-deletable roles
- Updated `DeleteRoleHandler` to return appropriate error responses

### 2. Org Creation Logic

**Enhanced Organization Creation:**
- Modified `createOrganization` function to automatically create CustomRoles and DeviceGroup
- Uses transaction-like approach with multiple database operations
- Creates CustomRoles for each TemplateRole matching the organization's type
- Copies `IsDeletable` from TemplateRole to CustomRole

**CustomRole Creation:**
- Automatically creates CustomRole for each TemplateRole matching the org's type
- Copies all fields from TemplateRole: `Identifier`, `Name`, `Description`, `IsDeletable`
- Sets `OrganizationId` to the new organization's ID
- Sets `OrgTypeIdentifier` to match the organization's type

**Default DeviceGroup Creation:**
- Creates a default DeviceGroup named "Default Devices"
- Associates it with the new organization
- Sets appropriate timestamps and soft delete flags

### 3. Database Schema Updates

**TemplateRole Table:**
```sql
ALTER TABLE {{TemplateRole}} ADD COLUMN IsDeletable BOOLEAN NOT NULL DEFAULT TRUE;
```

**CustomRole Table:**
```sql
ALTER TABLE {{CustomRole}} ADD COLUMN IsDeletable BOOLEAN NOT NULL DEFAULT TRUE;
```

**Migration Script:**
- `schemas/data-core-pg/1.0/updates/add_isdeletable_columns.sql`
- Updates existing data to set admin/anonymous roles as undeletable
- Copies IsDeletable from TemplateRole to existing CustomRole records

### 4. Application Code Changes

**Go Schema Updates:**
- Updated `CustomRole` struct to include `IsDeletable` field
- Updated `TemplateRole` struct to include `IsDeletable` field

**Organization Creation Logic:**
```go
// Enhanced createOrganization function
var createOrganization = func(pg connect.DatabaseExecutor, req *CreateAndUpdateOrganizationRequest) (*Organization, error) {
    // 1. Create organization
    // 2. Create CustomRoles for each TemplateRole matching org type
    // 3. Create default DeviceGroup
}
```

**Role Management Updates:**
- Updated `getRolesByOrganization` to include `IsDeletable` in queries
- Updated `createRole` to copy `IsDeletable` from TemplateRole
- Updated `deleteRole` to check `IsDeletable` before deletion
- Updated `getRoleTemplates` to include `IsDeletable` in results

**Error Handling:**
- Added `ErrRoleNotDeletable` error
- Updated `DeleteRoleHandler` to handle non-deletable roles

### 5. Seed Data Updates

**TemplateRole Seed Data:**
- Updated all seed files to include `IsDeletable` values
- Set admin/anonymous roles as `FALSE` (undeletable)
- Set other roles as `TRUE` (deletable)

**CustomRole Seed Data:**
- Updated existing CustomRole inserts to include `IsDeletable`
- Values copied from corresponding TemplateRole

### 6. Files Modified

**Database Schema:**
- `schemas/data-core-pg/1.0/DDL/schema.sql`
- `schemas/data-core-pg/1.0/updates/step3_run_ddl.sql`
- `schemas/data-core-pg/1.0/updates/add_isdeletable_columns.sql`

**Seed Data:**
- `schemas/data-core-pg/1.0/DML/data.sql`
- `schemas/data-core-pg/1.0/updates/step4_insert_seed_data.sql`
- `schemas/data-core-pg/1.0/updates/step5_migrate_data.sql`

**Application Code:**
- `shared/rest/onramp/organization/organization.go`
- `shared/rest/onramp/roles/schemas.go`
- `shared/rest/onramp/roles/roles.go`
- `shared/rest/onramp/roles/errors.go`

## Benefits

1. **Automatic Setup**: Every new organization starts with the correct roles and at least one device group
2. **Role Protection**: Critical admin and anonymous roles cannot be accidentally deleted
3. **Consistency**: All organizations have the same base structure
4. **Atomic Operations**: Organization creation is atomic with role and device group creation
5. **Backward Compatibility**: Existing organizations and roles continue to work

## Testing Considerations

1. **Organization Creation**: Verify that new organizations get CustomRoles and DeviceGroup
2. **Role Deletion**: Verify that admin/anonymous roles cannot be deleted
3. **Role Creation**: Verify that new roles copy IsDeletable from TemplateRole
4. **Migration**: Verify that existing data is properly migrated
5. **API Responses**: Verify that IsDeletable is included in API responses

## Deployment Notes

1. Run the migration script `add_isdeletable_columns.sql` on existing databases
2. Deploy the updated application code
3. Test organization creation to ensure roles and device groups are created
4. Verify that existing role deletion functionality works correctly
5. Monitor logs for any issues with the enhanced creation process 