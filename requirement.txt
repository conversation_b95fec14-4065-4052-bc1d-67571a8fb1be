Title:
As a Synapse User, when I create an organization, the backend should create defaults and prevent deletion of admin/anon roles

Description

When a new Organization is created, I need the system to automatically:

Mark the “admin” and “anonymous” {{CustomRoles}} as undeletable so that orgs cannot remove the all-grant or none-grant roles.

Generate a {{CustomRole}} for each {{TemplateRole}} matching that organization’s type.

Create a default {{DeviceGroup}} (e.g. “Default Devices”) for the new Organization.

so that every org starts with the correct roles and at least one device group out of the box.

Tasks:

Enforce Role Immutability

Add IsDeletable BOOLEAN NOT NULL DEFAULT TRUE to the {{TemplateRole}} table.

Add IsDeletable BOOLEAN NOT NULL to the {{CustomRole}} table (no default).

Write a data migration to set TemplateRole.IsDeletable = FALSE for identifiers 'admin' and 'anonymous'.

Org Creation Logic

When seeding CustomRole rows after creating an Organization, copy IsDeletable from each TemplateRole into the new CustomRole.

Extend the Organization handler in /shared/rest

After persisting the new Organization record, query all TemplateRole rows where OrgTypeIdentifier matches the orgs type.

For each template role, insert a CustomRole with:

OrganizationId = new orgs Id

TemplateRoleIdentifier, OrgTypeIdentifier, Name, Description copied from the template

Create default DeviceGroup

Immediately after org creation, insert a DeviceGroup with:

OrganizationId = new orgs Id

Name = e.g. "Default Device group" (or configurable default)